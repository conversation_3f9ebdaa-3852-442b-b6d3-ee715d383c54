from account.models import UserAccount
from django.db import models
from django.urls import reverse
from django_jsonform.models.fields import <PERSON><PERSON><PERSON><PERSON>
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db.models import Avg
from datetime import datetime, timedelta
from django.utils import timezone
# Create your models here.


class ProductCategory(models.Model):
    name = models.CharField(max_length=50, unique=True)

    # to make get link of the product according to the category

    def get_url(self):
        return reverse("product_by_category")

    def __str__(self):
        return self.name

    class Meta:
        db_table = "product_categories"
        ordering = ["name"]
        indexes = [
            models.Index(fields=["name"], name="idx_product_category_name"),
        ]


class Product(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    price = models.DecimalField(
        max_digits=10, decimal_places=2, validators=[MinValueValidator(0)]
    )

    category = models.ForeignKey(
        ProductCategory, on_delete=models.CASCADE, related_name="products"
    )
    rating = models.FloatField(
        validators=[MinValueValidator(0), MaxValueValidator(5)], default=0.0
    )
    in_stock = models.BooleanField(default=True)
    ingredients = JSONField(
        schema={
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "quantity": {"type": "string"},
                },
                "required": ["name", "quantity"],
            },
        },
        blank=True,
        null=True,
    )
    usage_instructions = models.TextField(blank=True, null=True)
    benefits = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    image = models.ImageField(upload_to="products/", blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    # Offer-related fields
    is_on_offer = models.BooleanField(default=False, help_text="Enable to add this product to offers")
    offer_price = models.DecimalField(
        max_digits=10, decimal_places=2, validators=[MinValueValidator(0)],
        blank=True, null=True, help_text="Discounted price when on offer"
    )
    offer_start_date = models.DateTimeField(blank=True, null=True, help_text="When the offer starts")
    offer_end_date = models.DateTimeField(blank=True, null=True, help_text="When the offer ends")
    offer_is_active = models.BooleanField(default=True, help_text="Whether the offer is currently active")

    class Meta:
        db_table = "products"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["name"], name="idx_product_name"),
            models.Index(fields=["category"], name="idx_product_category"),
            models.Index(fields=["rating"], name="idx_product_rating"),
            models.Index(fields=["price"], name="idx_product_price"),
            models.Index(fields=["in_stock"], name="idx_product_in_stock"),
            models.Index(fields=["is_on_offer"], name="idx_product_is_on_offer"),
            models.Index(fields=["offer_end_date"], name="idx_product_offer_end_date"),
        ]

    def save(self, *args, **kwargs):
        """Override save to handle offer logic"""
        now = timezone.now()

        # Auto-set offer start date if not provided and product is on offer
        if self.is_on_offer and not self.offer_start_date:
            self.offer_start_date = now

        # Auto-set offer end date if not provided (default 7 days)
        if self.is_on_offer and not self.offer_end_date:
            self.offer_end_date = now + timedelta(days=7)

        # Auto-deactivate offer if expired
        if self.offer_end_date and self.offer_end_date <= now:
            self.offer_is_active = False

        # Ensure offer price is set if product is on offer
        if self.is_on_offer and not self.offer_price:
            self.offer_price = self.price * 0.9  # Default 10% discount

        super().save(*args, **kwargs)

    @classmethod
    def deactivate_expired_offers(cls):
        """Class method to deactivate all expired offers"""
        now = timezone.now()
        expired_count = cls.objects.filter(
            is_on_offer=True,
            offer_is_active=True,
            offer_end_date__lte=now
        ).update(offer_is_active=False)
        return expired_count

    def __str__(self):
        return self.name

    # to get the specific product url inside the specific category

    def get_url(self):
        return reverse("product_detail", args=[self.category.slug, self.slug])

    # calculating the average rating of the product

    def averageRating(self):
        reviews = ReviewRating.objects.filter(product=self, status=True).aggregate(
            average=Avg("rating")
        )
        avg = 0
        if reviews["average"] is not None:
            avg = float(reviews["average"])
        return avg

    def reveiewCount(self):
        reviews = ReviewRating.objects.filter(product=self, status=True)
        return reviews.count()

    # Offer-related methods
    def is_offer_valid(self):
        """Check if the current offer is valid"""
        if not self.is_on_offer or not self.offer_is_active:
            return False

        now = timezone.now()
        if self.offer_start_date and self.offer_start_date > now:
            return False
        if self.offer_end_date and self.offer_end_date <= now:
            return False
        return True

    def get_current_price(self):
        """Get the current price (offer price if valid, otherwise regular price)"""
        if self.is_offer_valid() and self.offer_price:
            return self.offer_price
        return self.price

    def get_discount_percentage(self):
        """Calculate discount percentage"""
        if not self.is_offer_valid() or not self.offer_price:
            return 0
        if self.price <= 0:
            return 0
        return round(((self.price - self.offer_price) / self.price) * 100, 2)

    def get_savings_amount(self):
        """Calculate savings amount"""
        if not self.is_offer_valid() or not self.offer_price:
            return 0
        return self.price - self.offer_price


class VariationManager(models.Manager):
    def color(self):
        return super(VariationManager, self).filter(
            variation_category="color", is_active=True
        )


variation_category_choices = (("color", "color"),)


class Variation(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    variation_category = models.CharField(
        max_length=100, choices=variation_category_choices
    )
    variation_value = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    created_date = models.DateTimeField(auto_now=True)

    objects = VariationManager()  # calling the above method

    def __str__(self):
        return self.variation_value





class ReviewRating(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    user = models.ForeignKey(UserAccount, on_delete=models.CASCADE)
    subject = models.CharField(max_length=100, blank=True)
    review = models.TextField(blank=True)
    rating = models.FloatField()
    ip = models.CharField(max_length=20, blank=True)
    status = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.subject
