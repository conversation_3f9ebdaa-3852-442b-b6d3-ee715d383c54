from django.core.management.base import BaseCommand
from django.utils import timezone
from products.models import Product
from datetime import timedelta


class Command(BaseCommand):
    help = "Update expired product offers and manage offer lifecycle"

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )
        parser.add_argument(
            '--notify-expiring',
            type=int,
            default=24,
            help='Hours before expiration to notify (default: 24)',
        )

    def handle(self, *args, **options):
        now = timezone.now()
        dry_run = options['dry_run']
        notify_hours = options['notify_expiring']

        # Find expired offers
        expired_offers = Product.objects.filter(
            is_on_offer=True,
            offer_is_active=True,
            offer_end_date__lte=now
        )

        # Find offers expiring soon
        expiring_soon = Product.objects.filter(
            is_on_offer=True,
            offer_is_active=True,
            offer_end_date__gt=now,
            offer_end_date__lte=now + timedelta(hours=notify_hours)
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING(f"DRY RUN: Would deactivate {expired_offers.count()} expired offers")
            )
            self.stdout.write(
                self.style.WARNING(f"DRY RUN: {expiring_soon.count()} offers expiring in {notify_hours} hours")
            )

            for product in expired_offers:
                self.stdout.write(f"  - {product.name} (expired: {product.offer_end_date})")

            for product in expiring_soon:
                self.stdout.write(f"  - {product.name} (expires: {product.offer_end_date})")
        else:
            # Deactivate expired offers
            count = expired_offers.update(offer_is_active=False)

            self.stdout.write(
                self.style.SUCCESS(f"{count} expired offer(s) deactivated.")
            )

            # Log expiring offers
            if expiring_soon.exists():
                self.stdout.write(
                    self.style.WARNING(f"{expiring_soon.count()} offer(s) expiring in {notify_hours} hours:")
                )
                for product in expiring_soon:
                    self.stdout.write(f"  - {product.name} (expires: {product.offer_end_date})")

            # Summary statistics
            active_offers = Product.objects.filter(
                is_on_offer=True,
                offer_is_active=True,
                offer_end_date__gt=now
            ).count()

            self.stdout.write(
                self.style.SUCCESS(f"Summary: {active_offers} active offers remaining")
            )
